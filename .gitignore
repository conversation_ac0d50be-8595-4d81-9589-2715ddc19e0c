# ==================== Java ====================
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
replay_pid*

# ==================== Maven ====================
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# ==================== Gradle ====================
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# ==================== IDE ====================
# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# ==================== Spring Boot ====================
*.log
*.log.*
logs/
spring.log
application-*.properties
application-*.yml
!application-*.example.properties
!application-*.example.yml

# ==================== Docker & Kubernetes ====================
# Docker volumes and data
**/data/
**/logs/
**/log/
**/conf/admin.password

# Docker Compose override files
docker-compose.override.yml
docker-compose.override.yaml

# Kubernetes secrets
**/secrets/
*.secret.yaml
*.secret.yml

# ==================== Database ====================
# MySQL
infra/ecp-mysql/data/
infra/ecp-mysql/logs/
*.sql.backup
*.sql.dump

# Redis
ecp-redis/data/
dump.rdb
appendonly.aof

# ==================== Middleware Data ====================
# Nacos
ecp-nacos/data/
ecp-nacos/logs/

# Nexus
ecp-nexus/data/
ecp-nexus/log/
ecp-nexus/work/

# MinIO
ecp-minio/data/
ecp-minio/conf/

# Kafka
ecp-kafka/data/
ecp-kafka/logs/

# XXL-Job
ecp-xxl-job/

# ==================== Environment & Config ====================
# Environment variables
.env
.env.local
.env.*.local
*.env

# Configuration files with sensitive data
**/application-prod.yml
**/application-prod.yaml
**/application-prod.properties
**/bootstrap-prod.yml
**/bootstrap-prod.yaml
**/bootstrap-prod.properties

# ==================== System Files ====================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==================== Temporary & Cache ====================
# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~
.#*
\#*#

# Cache directories
.cache/
*.cache
node_modules/
.npm
.yarn/

# ==================== Backup & Archive ====================
*.bak
*.backup
*.old
*.orig
*.save
*.zip
*.tar.gz
*.rar
*.7z

# ==================== Testing ====================
# Test results
TEST-*.xml
junit.xml
coverage/
.coverage
htmlcov/
.tox/
.pytest_cache/
.testmondata

# JaCoCo
jacoco.exec
jacoco-it.exec
**/target/site/jacoco/

# ==================== Monitoring & Profiling ====================
# JVM dumps
*.hprof
*.dump

# Profiling
*.prof

# ==================== Security ====================
# Private keys
*.pem
*.key
*.p12
*.jks
*.keystore
*.truststore

# Certificates
*.crt
*.cer
*.der

# ==================== Custom Project Specific ====================
# ECP specific data directories
init/mysql/data/
init/minio/data/

# Generated files
**/generated/
**/gen/

# Build artifacts
app.jar
*.jar.original

# Local development overrides
docker-compose.local.yml
docker-compose.dev.yml
