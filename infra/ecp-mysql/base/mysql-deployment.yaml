apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: ecp  # 添加命名空间
  labels:
    app: mysql
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      # 添加Pod级别的安全上下文
      securityContext:
        fsGroup: 999  # 确保挂载的卷具有正确的组权限
        runAsUser: 999
      containers:
        - name: mysql
          image: mysql:8.4.5
          imagePullPolicy: IfNotPresent
          # 添加容器级别的安全上下文
          securityContext:
            runAsUser: 999
            runAsGroup: 999
          env:
            - name: MYSQL_ROOT_PASSWORD
              valueFrom:
                configMapKeyRef:
                  name: mysql-config
                  key: MYSQL_ROOT_PASSWORD
            - name: TZ
              value: Asia/Shanghai
            - name: MYSQL_INNODB_BUFFER_POOL_SIZE
              value: "1536M"
            - name: MYSQL_INNODB_LOG_BUFFER_SIZE
              value: "64M"
            - name: MYSQL_MAX_CONNECTIONS
              value: "200"
            - name: MYSQL_QUERY_CACHE_SIZE
              value: "128M"
          ports:
            - containerPort: 3306
              name: mysql
          volumeMounts:
            - name: mysql-data
              mountPath: /var/lib/mysql
            - name: mysql-log
              mountPath: /var/log/mysql
            - name: mysql-config-files
              mountPath: /etc/mysql/conf.d
              readOnly: true
            - name: mysql-init
              mountPath: /docker-entrypoint-initdb.d
              readOnly: true
            - name: localtime
              mountPath: /etc/localtime
              readOnly: true
          readinessProbe:
            exec:
              command:
                - sh
                - -c
                - "mysqladmin ping -h 127.0.0.1 -p$(MYSQL_ROOT_PASSWORD)"
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 10
      volumes:
        - name: mysql-data
          persistentVolumeClaim:
            claimName: mysql-data-pvc
        - name: mysql-log
          persistentVolumeClaim:
            claimName: mysql-log-pvc
        - name: mysql-config-files
          configMap:
            name: mysql-config-files
        - name: mysql-init
          persistentVolumeClaim:
            claimName: mysql-init-pvc
        - name: localtime
          hostPath:
            path: /etc/localtime
