apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-config-files
  namespace: ecp
data:
  my.cnf: |
    ###### [client]配置模块 ######
    [client]
    default-character-set=utf8mb4
    socket=/var/lib/mysql/mysql.sock

    ###### [mysql]配置模块 ######
    [mysql]
    # 设置MySQL客户端默认字符集
    default-character-set=utf8mb4
    socket=/var/lib/mysql/mysql.sock

    ###### [mysqld]配置模块 ######
    [mysqld]
    port=3306
    user=mysql
    # 设置sql模式 sql_mode模式引起的分组查询出现*this is incompatible with sql_mode=only_full_group_by，这里最好剔除ONLY_FULL_GROUP_BY
    sql_mode=STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
    datadir=/var/lib/mysql
    socket=/var/lib/mysql/mysql.sock
    server-id=1

    # 禁用符号链接以防止各种安全风险
    symbolic-links=0

    # 允许最大连接数
    max_connections=1000

    # 服务端使用的字符集默认为8比特编码的latin1字符集
    character-set-server=utf8mb4

    # 创建新表时将使用的默认存储引擎
    default-storage-engine=INNODB

    # 0: 表名将按指定方式存储，并且比较区分大小写;
    # 1: 表名以小写形式存储在磁盘上，比较不区分大小写；
    lower_case_table_names=1

    max_allowed_packet=16M

    # 设置时区
    default-time_zone='+8:00'

    # 慢查询日志（可选）
    slow_query_log=1
    slow_query_log_file=/var/log/mysql/slow.log
    long_query_time=2
