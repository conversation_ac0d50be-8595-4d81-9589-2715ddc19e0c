apiVersion: v1
kind: PersistentVolume
metadata:
  name: mysql-data-pv
spec:
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 10Gi
  nfs:
    server: 192.168.64.2
    path: /mnt/nfs-share/ecp/ecp-env-k8s/ecp-mysql/data
  persistentVolumeReclaimPolicy: Retain
  storageClassName: nfs-storage
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: mysql-init-pv
spec:
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 1Gi
  nfs:
    server: 192.168.64.2
    path: /mnt/nfs-share/ecp/ecp-env-k8s/ecp-mysql/init
  persistentVolumeReclaimPolicy: Retain
  storageClassName: nfs-storage
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: mysql-log-pv
spec:
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 1Gi
  nfs:
    server: 192.168.64.2
    path: /mnt/nfs-share/ecp/ecp-env-k8s/ecp-mysql/log
  persistentVolumeReclaimPolicy: Retain
  storageClassName: nfs-storage