# MinIO 部署到 hui 哥 K8s 集群指南

## 📋 环境信息

### 目标集群信息
- **Kubernetes 版本**: 1.33.0
- **架构**: ARM64 (Apple Silicon)
- **宿主机**: ************* (CBU-TT-02)
- **Master 节点**: ************ (2C 4G 20G)
- **Worker1 节点**: ************ (2C 8G 60G)
- **Worker2 节点**: ************ (2C 8G 60G)

### 项目路径
- **hui 哥 Mac 项目路径**: `/Users/<USER>/capitalization/ecp/ecp-env-k8s`
- **共享目录映射**: `/Users/<USER>/capitalization` → `/home/<USER>/capitalization`

### SSH 连接别名
```bash
alias master="ssh ubuntu@************"
alias worker1="ssh ubuntu@************"
alias worker2="ssh ubuntu@************"
```

### ⚠️ 重要提醒：ARM64 架构注意事项
由于 hui 哥的集群运行在 Apple Silicon (ARM64) 架构上，需要使用专门的 ARM64 版本脚本：
- 使用 `uploadScript-arm64.sh` 而不是 `uploadScript.sh`
- MinIO 客户端会自动下载 ARM64 版本
- 所有容器镜像都会自动拉取 ARM64 版本

## 🚀 部署步骤详解

### 步骤 1：连接到 hui 哥的环境

```bash
# 连接到宿主机
ssh yihuliu@*************
```

**含义**：
- 通过 SSH 连接到 hui 哥的 Mac 宿主机
- 宿主机作为 K8s 集群的管理节点，kubectl 命令在此执行

### 步骤 2：进入项目目录

```bash
# 进入项目目录
cd /Users/<USER>/capitalization/ecp/ecp-env-k8s

# 确认目录内容
ls -la
```

**含义**：
- 切换到 MinIO 部署配置所在的项目目录
- 该目录包含所有必要的配置文件和脚本
- 与您本地的项目是同一套代码（通过 Git 同步）

### 步骤 3：检查集群状态

```bash
# 检查集群连接状态
kubectl cluster-info

# 查看节点状态
kubectl get nodes -o wide

# 检查存储类
kubectl get storageclass

# 检查当前命名空间
kubectl get namespaces
```

**含义**：
- **cluster-info**: 验证 kubectl 能正常连接到 K8s 集群
- **get nodes**: 确认所有节点（master + 2 workers）状态正常
- **get storageclass**: 查看可用的存储类，MinIO 需要持久化存储
- **get namespaces**: 检查是否已存在 ecp 命名空间

**预期输出示例**：
```
NAME     STATUS   ROLES           AGE   VERSION   INTERNAL-IP     EXTERNAL-IP
master   Ready    control-plane   XXd   v1.33.0   ************    <none>
worker1  Ready    <none>          XXd   v1.33.0   ************    <none>
worker2  Ready    <none>          XXd   v1.33.0   ************    <none>
```

### 步骤 4：检查 MinIO 配置文件

```bash
# 查看 MinIO 配置文件
ls -la infra/minio/

# 检查初始化数据
ls -la init/minio/
find init/minio/s3files -type f | wc -l
```

**含义**：
- 确认 MinIO 的 K8s 配置文件存在
- 验证初始化数据（s3files）完整性
- 统计需要上传的文件数量（应该是 892 个文件）

### 步骤 5：使用自动化脚本部署

```bash
# 进入 MinIO 配置目录
cd infra/minio

# 给部署脚本执行权限
chmod +x deploy-minio.sh

# 执行自动化部署
./deploy-minio.sh
```

**含义**：
- **chmod +x**: 给脚本添加执行权限
- **deploy-minio.sh**: 执行自动化部署脚本，包含以下步骤：
  1. 检查依赖工具（kubectl、docker）
  2. 验证 K8s 集群连接
  3. 创建 ecp 命名空间
  4. 检查初始化数据完整性
  5. 应用 MinIO K8s 配置
  6. 等待 MinIO Pod 启动
  7. 测试服务连通性
  8. 显示连接信息

**预期输出**：
```
[INFO] 开始部署 MinIO 到 Kubernetes...
[SUCCESS] 依赖检查通过
[SUCCESS] 已连接到 Kubernetes 集群: kubernetes-admin@kubernetes
[SUCCESS] 命名空间 ecp 创建成功
[SUCCESS] 初始化数据检查通过
[SUCCESS] MinIO 配置应用成功
[SUCCESS] MinIO Deployment 就绪
[SUCCESS] MinIO Pod 运行正常
[SUCCESS] MinIO API 端口 (30900) 连通正常
[SUCCESS] MinIO 管理控制台端口 (30901) 连通正常
[SUCCESS] MinIO 部署完成！
```

### 步骤 6：手动复制初始化数据（重要）

由于初始化 Job 可能因为 PVC 中没有数据而失败，需要手动复制：

```bash
# 返回项目根目录
cd /Users/<USER>/capitalization/ecp/ecp-env-k8s

# 创建临时 Pod 用于数据复制
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: temp-copy-pod
  namespace: ecp
spec:
  restartPolicy: Never
  containers:
  - name: copy-container
    image: busybox:latest
    command: ['sleep', '3600']
    volumeMounts:
    - name: minio-init-scripts
      mountPath: /mnt/init
  volumes:
  - name: minio-init-scripts
    persistentVolumeClaim:
      claimName: minio-init-pvc
EOF

# 等待 Pod 运行
kubectl wait --for=condition=ready --timeout=60s pod/temp-copy-pod -n ecp

# 复制初始化数据到 PVC
kubectl cp init/minio/ ecp/temp-copy-pod:/mnt/init/ -c copy-container

# 调整文件路径（移动到正确位置）
kubectl exec temp-copy-pod -n ecp -c copy-container -- sh -c "cp -r /mnt/init/minio/* /mnt/init/ && rm -rf /mnt/init/minio"

# 验证文件复制成功
kubectl exec temp-copy-pod -n ecp -c copy-container -- ls -la /mnt/init/

# 删除临时 Pod
kubectl delete pod temp-copy-pod -n ecp
```

**含义**：
- **临时 Pod**: 创建一个临时容器来访问 PVC
- **kubectl cp**: 将本地初始化数据复制到 K8s PVC 中
- **文件路径调整**: 确保文件在初始化脚本期望的位置
- **验证**: 确认所有文件（uploadScript.sh、assetMapping.csv、s3files/）都已正确复制

### 步骤 7：重新启动初始化 Job

```bash
# 删除失败的初始化 Job
kubectl delete job ecp-minio-init-job -n ecp

# 重新创建初始化 Job
kubectl apply -f infra/minio/minio-k8s.yaml
```

**含义**：
- 删除之前可能失败的初始化 Job
- 重新创建 Job，此时 PVC 中已有初始化数据
- Job 将执行文件上传到 MinIO 的任务

### 步骤 8：监控初始化进度

```bash
# 查看初始化 Job 状态
kubectl get jobs -l app=ecp-minio-init -n ecp

# 实时查看初始化日志
kubectl logs -l app=ecp-minio-init -n ecp -f

# 查看最近的日志（如果不想实时监控）
kubectl logs -l app=ecp-minio-init -n ecp --tail=50
```

**含义**：
- **get jobs**: 查看 Job 的执行状态（Running/Complete/Failed）
- **logs -f**: 实时查看初始化过程，包括文件上传进度
- 初始化过程包括：
  1. 下载 MinIO 客户端工具
  2. 安装 csvkit 工具
  3. 创建 3 个 bucket
  4. 根据 assetMapping.csv 上传 892 个文件
  5. 预计耗时 10-20 分钟

### 步骤 9：验证部署结果

```bash
# 检查所有相关资源状态
kubectl get all -l app=ecp-minio -n ecp

# 检查 PVC 状态
kubectl get pvc -n ecp

# 测试 MinIO 连通性
kubectl exec -it deployment/ecp-minio -n ecp -- mc alias set local http://localhost:9000 admin 'capitalization@DTT#ecp'

# 查看创建的 bucket
kubectl exec -it deployment/ecp-minio -n ecp -- mc ls local

# 统计上传的文件数量
kubectl exec -it deployment/ecp-minio -n ecp -- mc ls local/s3-atb-ecp-image-test/image/ | wc -l
kubectl exec -it deployment/ecp-minio -n ecp -- mc ls local/s3-atb-ecp-file-test/file/ --recursive | wc -l
kubectl exec -it deployment/ecp-minio -n ecp -- mc ls local/s3-atb-ecp-video-test/video/ | wc -l
```

**含义**：
- 验证所有 K8s 资源正常运行
- 确认数据持久化正常
- 测试 MinIO 内部连接
- 统计各类型文件上传成功数量

## 🌐 访问 MinIO

### 通过 NodePort 访问

由于使用 NodePort 服务，可以通过任意节点 IP 访问：

```bash
# API 访问地址（端口 30900）
http://************:30900  # Master
http://************:30900  # Worker1  
http://************:30900  # Worker2

# 管理控制台（端口 30901）
http://************:30901  # Master
http://************:30901  # Worker1
http://************:30901  # Worker2
```

### 通过端口转发访问（从宿主机）

```bash
# 在宿主机上执行端口转发
kubectl port-forward -n ecp svc/ecp-minio-service 9000:9000 9001:9001

# 然后在浏览器访问
http://localhost:9001
```

### 登录信息
- **用户名**: admin
- **密码**: capitalization@DTT#ecp

## 📊 预期结果

部署成功后，您将获得：

1. **3 个 MinIO Bucket**：
   - `s3-atb-ecp-image-test` - 图片文件
   - `s3-atb-ecp-file-test` - 文档文件  
   - `s3-atb-ecp-video-test` - 视频文件

2. **文件上传统计**：
   - 图片文件：~296 个
   - 文档文件：~550 个
   - 视频文件：~14 个
   - 总计：~860 个文件

3. **持久化存储**：
   - 数据存储：20Gi
   - 配置存储：1Gi
   - 初始化脚本：5Gi

## 🔧 故障排查

如果遇到问题，可以使用以下命令排查：

```bash
# 查看 Pod 详细状态
kubectl describe pod -l app=ecp-minio -n ecp

# 查看 MinIO 日志
kubectl logs -l app=ecp-minio -n ecp

# 查看初始化 Job 日志
kubectl logs -l app=ecp-minio-init -n ecp

# 查看事件
kubectl get events -n ecp --sort-by=.metadata.creationTimestamp
```

## ✅ 部署完成确认

部署成功的标志：
- [ ] MinIO Pod 状态为 Running
- [ ] 初始化 Job 状态为 Complete  
- [ ] 可以通过浏览器访问管理控制台
- [ ] 3 个 bucket 已创建
- [ ] 文件上传数量符合预期
