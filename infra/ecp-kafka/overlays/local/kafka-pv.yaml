apiVersion: v1
kind: PersistentVolume
metadata:
  name: ecp-kafka-data-pv
spec:
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 2Gi
  nfs:
    server: 192.168.64.2
    path: /mnt/nfs-share/ecp/ecp-env-k8s/ecp-kafka/data
  persistentVolumeReclaimPolicy: Retain
  storageClassName: nfs-storage
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: ecp-kafka-metadata-pv
spec:
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 1Gi
  nfs:
    server: 192.168.64.2
    path: /mnt/nfs-share/ecp/ecp-env-k8s/ecp-kafka/metadata
  persistentVolumeReclaimPolicy: Retain
  storageClassName: nfs-storage
