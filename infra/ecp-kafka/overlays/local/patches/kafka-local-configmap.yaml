apiVersion: v1
kind: ConfigMap
metadata:
  name: ecp-kafka-config
  namespace: ecp
data:
  # Kafka KRaft mode configuration (without Zookeeper) - Local Override
  KAFKA_CFG_LISTENERS: "PLAINTEXT://0.0.0.0:9092,CONTROLLER://0.0.0.0:9093"
  KAFKA_CFG_ADVERTISED_LISTENERS: "PLAINTEXT://localhost:9092,CONTROLLER://localhost:9093"
  KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: "CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT"
  KAFKA_CFG_CONTROLLER_QUORUM_VOTERS: "1@localhost:9093"

  # Kafka JVM optimization - 1.5GB
  KAFKA_HEAP_OPTS: "-Xms512m -Xmx1536m"
  KAFKA_JVM_PERFORMANCE_OPTS: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions"

  # Kafka performance optimization
  KAFKA_CFG_NUM_NETWORK_THREADS: "8"
  KAFKA_CFG_NUM_IO_THREADS: "8"
  KAFKA_CFG_SOCKET_SEND_BUFFER_BYTES: "102400"
  KAFKA_CFG_SOCKET_RECEIVE_BUFFER_BYTES: "102400"
  KAFKA_CFG_SOCKET_REQUEST_MAX_BYTES: "104857600"
