apiVersion: v1
kind: ConfigMap
metadata:
  name: ecp-kafka-config
  namespace: ecp
data:
  # Kafka KRaft mode configuration (without Zookeeper)
  KAFKA_CFG_LISTENERS: "PLAINTEXT://0.0.0.0:9092,CONTROLLER://0.0.0.0:9093"
  KAFKA_CFG_ADVERTISED_LISTENERS: "PLAINTEXT://172.16.16.181:30092,CONTROLLER://10.43.71.166:9093"
  KAFKA_CFG_CONTROLLER_QUORUM_VOTERS: "1@10.43.71.166:9093"
  
  KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: "CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT"
  KAFKA_CFG_INTER_BROKER_LISTENER_NAME: "PLAINTEXT"
  KAFKA_KRAFT_CLUSTER_ID: "b2a6ee0f-6b91-4d4f-94f9-f8f0a6c4b1c9"

  # Kafka JVM optimization - 1.5GB
  KAFKA_HEAP_OPTS: "-Xms512m -Xmx1536m"
  KAFKA_JVM_PERFORMANCE_OPTS: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions"
  
  # Kafka performance optimization
  KAFKA_CFG_NUM_NETWORK_THREADS: "8"
  KAFKA_CFG_NUM_IO_THREADS: "8"
  KAFKA_CFG_SOCKET_SEND_BUFFER_BYTES: "102400"
  KAFKA_CFG_SOCKET_RECEIVE_BUFFER_BYTES: "102400"
  KAFKA_CFG_SOCKET_REQUEST_MAX_BYTES: "104857600"
  
