apiVersion: v1
kind: Service
metadata:
  name: ecp-kafka
  namespace: ecp
  labels:
    app: ecp-kafka
spec:
  type: ClusterIP
  ports:
    - name: kafka
      port: 9092
      targetPort: 9092
      protocol: TCP
    - name: controller
      port: 9093
      targetPort: 9093
      protocol: TCP
  selector:
    app: ecp-kafka
---
apiVersion: v1
kind: Service
metadata:
  name: ecp-kafka-external
  namespace: ecp
  labels:
    app: ecp-kafka
spec:
  type: NodePort
  ports:
    - name: kafka
      port: 9092
      targetPort: 9092
      nodePort: 30092
      protocol: TCP
#    - name: controller
#      port: 9093
#      targetPort: 9093
#      nodePort: 30093
#      protocol: TCP
  selector:
    app: ecp-kafka
