#!/bin/bash

echo "=== Kafka 部署和网络连通性测试脚本 ==="
echo "目标: 从宿主机 172.16.16.181 访问 Kafka ************:30092"
echo

# 设置别名
alias master="ssh ubuntu@************"
alias worker1="ssh ubuntu@192.168.64.3"
alias worker2="ssh ubuntu@192.168.64.4"

# 检查当前目录
if [ ! -f "overlays/local/kustomization.yaml" ]; then
    echo "错误: 请在 infra/ecp-kafka 目录下运行此脚本"
    exit 1
fi

echo "1. 检查 K8s 集群状态..."
ssh ubuntu@************ "kubectl get nodes -o wide"

echo
echo "2. 删除现有的 Kafka 部署..."
ssh ubuntu@************ "kubectl delete deployment ecp-kafka -n ecp --ignore-not-found=true"
ssh ubuntu@************ "kubectl delete pod -l app=ecp-kafka -n ecp --force --grace-period=0 --ignore-not-found=true"
ssh ubuntu@************ "kubectl delete configmap ecp-kafka-config -n ecp --ignore-not-found=true"

echo
echo "3. 等待资源清理..."
sleep 10

echo
echo "4. 上传配置文件到 master 节点..."
scp -r overlays/local ubuntu@************:~/kafka-config/

echo
echo "5. 在 master 节点部署 Kafka..."
ssh ubuntu@************ "cd ~/kafka-config && kubectl apply -k ."

echo
echo "6. 等待 Pod 启动..."
sleep 20

echo
echo "7. 检查 Pod 状态..."
ssh ubuntu@************ "kubectl get pods -n ecp -l app=ecp-kafka -o wide"

echo
echo "8. 检查服务状态..."
ssh ubuntu@************ "kubectl get svc -n ecp -l app=ecp-kafka"

echo
echo "9. 检查 NodePort 服务..."
ssh ubuntu@************ "kubectl get svc ecp-kafka-external -n ecp -o yaml | grep nodePort"

echo
echo "10. 检查 Pod 日志..."
POD_NAME=$(ssh ubuntu@************ "kubectl get pods -n ecp -l app=ecp-kafka -o jsonpath='{.items[0].metadata.name}'" 2>/dev/null)
if [ ! -z "$POD_NAME" ]; then
    echo "Pod 名称: $POD_NAME"
    ssh ubuntu@************ "kubectl logs $POD_NAME -n ecp --tail=10"
else
    echo "未找到 Kafka Pod"
fi

echo
echo "11. 测试从 master 节点内部连接..."
if [ ! -z "$POD_NAME" ]; then
    ssh ubuntu@************ "kubectl exec $POD_NAME -n ecp -- kafka-topics.sh --bootstrap-server localhost:9092 --list"
fi

echo
echo "12. 测试从宿主机连接 ************:30092..."
echo "检查端口是否开放..."
nc -zv ************ 30092

echo
echo "=== 部署完成 ==="
echo "如果连接失败，请检查:"
echo "1. K8s 节点防火墙设置"
echo "2. NodePort 服务是否正确暴露"
echo "3. Kafka 配置中的 advertised.listeners"
