# ECP Kafka Kubernetes Deployment

这个目录包含了在 Kubernetes 集群中部署 Kafka 3.9 的配置文件。

## 特性

- **Kafka 3.9**: 使用最新的 Kafka 3.9 版本
- **KRaft 模式**: 无需 Zookeeper，使用 Kafka 内置的 KRaft 共识协议
- **持久化存储**: 数据和元数据分别使用独立的 PVC
- **资源优化**: 配置了合适的 CPU 和内存限制
- **健康检查**: 包含 liveness 和 readiness 探针
- **服务暴露**: 提供集群内部和外部访问方式

## 文件说明

- `configmap.yaml`: Kafka 配置参数
- `pvc.yaml`: 持久化存储声明
- `service.yaml`: 服务定义（内部和外部访问）
- `deployment.yaml`: Kafka 部署配置
- `kustomization.yaml`: Kustomize 配置文件
- `README.md`: 部署说明文档

## 部署步骤

### 1. 使用 kubectl 直接部署

```bash
# 部署所有资源
kubectl apply -f .

# 或者按顺序部署
kubectl apply -f pvc.yaml
kubectl apply -f configmap.yaml
kubectl apply -f service.yaml
kubectl apply -f deployment.yaml
```

### 2. 使用 kustomize 部署

```bash
kubectl apply -k .
```

## 验证部署

```bash
# 检查 Pod 状态
kubectl get pods -l app=ecp-kafka

# 检查服务状态
kubectl get svc -l app=ecp-kafka

# 查看 Pod 日志
kubectl logs -l app=ecp-kafka

# 检查 PVC 状态
kubectl get pvc
```

## 访问 Kafka

### 集群内部访问
- 服务名: `ecp-kafka`
- 端口: `9092`
- 连接字符串: `ecp-kafka:9092`

### 集群外部访问
- NodePort: `30092`
- 连接字符串: `<节点IP>:30092`

## 测试 Kafka

```bash
# 进入 Kafka Pod
kubectl exec -it deployment/ecp-kafka -- bash

# 创建测试主题
kafka-topics.sh --bootstrap-server localhost:9092 --create --topic test-topic --partitions 1 --replication-factor 1

# 列出主题
kafka-topics.sh --bootstrap-server localhost:9092 --list

# 发送消息
kafka-console-producer.sh --bootstrap-server localhost:9092 --topic test-topic

# 接收消息（在另一个终端）
kafka-console-consumer.sh --bootstrap-server localhost:9092 --topic test-topic --from-beginning
```

## 配置说明

### 资源配置
- **CPU**: 请求 500m，限制 1500m
- **内存**: 请求 512Mi，限制 1024Mi
- **存储**: 数据 10Gi，元数据 1Gi

### JVM 配置
- **堆内存**: 512m - 1536m
- **垃圾收集器**: G1GC
- **最大 GC 暂停时间**: 200ms

### 网络配置
- **网络线程数**: 8
- **IO 线程数**: 8
- **发送缓冲区**: 102400 字节
- **接收缓冲区**: 102400 字节

## 清理资源

```bash
# 删除所有资源
kubectl delete -k .

# 或者
kubectl delete -f .
```

## 注意事项

1. **存储类**: 默认使用 `standard` 存储类，请根据集群环境调整
2. **命名空间**: 默认部署在 `default` 命名空间
3. **单实例**: 当前配置为单实例部署，生产环境建议配置多实例
4. **安全性**: 当前使用 PLAINTEXT 协议，生产环境建议启用 SSL/SASL
