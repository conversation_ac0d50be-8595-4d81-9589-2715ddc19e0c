#!/bin/bash

echo "=== Kafka KRaft 配置修复和重新部署脚本 ==="
echo

# 检查当前目录
if [ ! -f "overlays/local/kustomization.yaml" ]; then
    echo "错误: 请在 infra/ecp-kafka 目录下运行此脚本"
    exit 1
fi

# 1. 删除现有的 Kafka 部署
echo "1. 删除现有的 Kafka 部署..."
kubectl delete deployment ecp-kafka -n ecp --ignore-not-found=true
kubectl delete pod -l app=ecp-kafka -n ecp --force --grace-period=0 --ignore-not-found=true

# 2. 删除现有的 ConfigMap
echo "2. 删除现有的 ConfigMap..."
kubectl delete configmap ecp-kafka-config -n ecp --ignore-not-found=true

# 3. 等待资源清理
echo "3. 等待资源清理..."
sleep 10

# 4. 重新应用配置
echo "4. 重新应用 Kafka 配置..."
kubectl apply -k overlays/local/

# 5. 等待 Pod 启动
echo "5. 等待 Pod 启动..."
sleep 15

# 6. 检查部署状态
echo "6. 检查部署状态..."
kubectl get pods -n ecp -l app=ecp-kafka -o wide

echo
echo "7. 检查 Pod 日志..."
POD_NAME=$(kubectl get pods -n ecp -l app=ecp-kafka -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
if [ ! -z "$POD_NAME" ]; then
    echo "Pod 名称: $POD_NAME"
    echo "最近的日志:"
    kubectl logs $POD_NAME -n ecp --tail=20
else
    echo "未找到 Kafka Pod"
fi

echo
echo "8. 检查服务状态..."
kubectl get svc -n ecp -l app=ecp-kafka

echo
echo "=== 修复完成 ==="
echo "如果仍有问题，请检查:"
echo "1. kubectl describe pod <pod-name> -n ecp"
echo "2. kubectl logs <pod-name> -n ecp"
echo "3. kubectl get events -n ecp --sort-by='.lastTimestamp'"
