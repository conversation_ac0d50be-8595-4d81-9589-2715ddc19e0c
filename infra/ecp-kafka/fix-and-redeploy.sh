#!/bin/bash

echo "=== Kafka 部署和网络连通性测试脚本 ==="
echo "目标: 从宿主机 172.16.16.181 访问 Kafka ************:30092"
echo "使用共享目录: /home/<USER>/capitalization/ecp/ecp-env-k8s"
echo

# 检查当前目录
if [ ! -f "overlays/local/kustomization.yaml" ]; then
    echo "错误: 请在 infra/ecp-kafka 目录下运行此脚本"
    exit 1
fi

echo "1. 检查 K8s 集群状态..."
ssh ubuntu@************ "kubectl get nodes -o wide"

echo
echo "2. 删除现有的 Kafka 部署..."
ssh ubuntu@************ "kubectl delete deployment ecp-kafka -n ecp --ignore-not-found=true"
ssh ubuntu@************ "kubectl delete pod -l app=ecp-kafka -n ecp --force --grace-period=0 --ignore-not-found=true"
ssh ubuntu@************ "kubectl delete configmap ecp-kafka-config -n ecp --ignore-not-found=true"

echo
echo "3. 等待资源清理..."
sleep 10

echo
echo "4. 在 master 节点部署 Kafka (使用共享目录)..."
ssh ubuntu@************ "cd /home/<USER>/capitalization/ecp/ecp-env-k8s/infra/ecp-kafka && kubectl apply -k overlays/local/"

echo
echo "5. 等待 Pod 启动..."
sleep 20

echo
echo "6. 检查 Pod 状态..."
ssh ubuntu@************ "kubectl get pods -n ecp -l app=ecp-kafka -o wide"

echo
echo "7. 检查服务状态..."
ssh ubuntu@************ "kubectl get svc -n ecp -l app=ecp-kafka"

echo
echo "8. 检查 NodePort 服务..."
ssh ubuntu@************ "kubectl get svc ecp-kafka-external -n ecp -o wide"

echo
echo "9. 检查 Pod 日志..."
POD_NAME=$(ssh ubuntu@************ "kubectl get pods -n ecp -l app=ecp-kafka -o jsonpath='{.items[0].metadata.name}'" 2>/dev/null)
if [ ! -z "$POD_NAME" ]; then
    echo "Pod 名称: $POD_NAME"
    echo "最近的日志:"
    ssh ubuntu@************ "kubectl logs $POD_NAME -n ecp --tail=15"
else
    echo "未找到 Kafka Pod"
fi

echo
echo "10. 测试从 master 节点内部连接..."
if [ ! -z "$POD_NAME" ]; then
    echo "测试 Kafka 内部连接..."
    ssh ubuntu@************ "kubectl exec $POD_NAME -n ecp -- kafka-topics.sh --bootstrap-server localhost:9092 --list" 2>/dev/null || echo "内部连接测试失败"
fi

echo
echo "11. 测试从宿主机连接 ************:30092..."
echo "检查端口是否开放..."
nc -zv ************ 30092 2>/dev/null && echo "✅ 端口 30092 可达" || echo "❌ 端口 30092 不可达"

echo
echo "12. 检查防火墙和网络..."
ssh ubuntu@************ "sudo netstat -tlnp | grep :30092" || echo "端口 30092 未在监听"

echo
echo "=== 部署完成 ==="
echo "连接信息:"
echo "- 集群内部: ecp-kafka:9092"
echo "- 外部访问: ************:30092"
echo
echo "如果连接失败，请检查:"
echo "1. kubectl describe pod $POD_NAME -n ecp"
echo "2. kubectl logs $POD_NAME -n ecp"
echo "3. kubectl get events -n ecp --sort-by='.lastTimestamp'"
