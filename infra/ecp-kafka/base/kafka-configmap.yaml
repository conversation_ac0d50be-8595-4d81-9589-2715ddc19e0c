apiVersion: v1
kind: ConfigMap
metadata:
  name: ecp-kafka-config
  namespace: ecp
data:
  # Kafka KRaft mode configuration (without Zookeeper)
  KAFKA_ENABLE_KRAFT: "yes"
  KAFKA_CFG_PROCESS_ROLES: "controller,broker"
  KAFKA_CFG_NODE_ID: "1"
  KAFKA_CFG_CONTROLLER_QUORUM_VOTERS: "1@192.168.64.2:9093"
  KAFKA_CFG_CONTROLLER_LISTENER_NAMES: "CONTROLLER"

  # Basic Kafka configuration
  KAFKA_BROKER_ID: "1"
  ALLOW_PLAINTEXT_LISTENER: "yes"
  TZ: "Asia/Shanghai"
  
  # KRaft specific configurations
  KAFKA_CFG_LOG_DIRS: "/bitnami/kafka/data"
  KAFKA_CFG_METADATA_LOG_DIR: "/bitnami/kafka/metadata"
