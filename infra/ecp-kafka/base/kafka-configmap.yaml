apiVersion: v1
kind: ConfigMap
metadata:
  name: ecp-kafka-config
  namespace: ecp
data:
  # Kafka KRaft mode configuration (without Zookeeper)
  KAFKA_ENABLE_KRAFT: "yes"
  KAFKA_CFG_PROCESS_ROLES: "controller,broker"
  <PERSON>AF<PERSON>_CFG_NODE_ID: "1"
  KAF<PERSON>_CFG_CONTROLLER_LISTENER_NAMES: "CONTROLLER"
  KAFKA_CFG_LISTENERS: "PLAINTEXT://0.0.0.0:9092,CONTROLLER://0.0.0.0:9093"
  KAFKA_CFG_ADVERTISED_LISTENERS: "PLAINTEXT://ecp-kafka:9092"
  KAFKA_CFG_CONTROLLER_QUORUM_VOTERS: "1@ecp-kafka:9093"
  KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: "CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT"
  KAFKA_CFG_INTER_BROKER_LISTENER_NAME: "PLAINTEXT"
  KAFKA_KRAFT_CLUSTER_ID: "b2a6ee0f-6b91-4d4f-94f9-f8f0a6c4b1c9"

  # Basic Kafka configuration
  KAFKA_BROKER_ID: "1"
  ALLOW_PLAINTEXT_LISTENER: "yes"
  TZ: "Asia/Shanghai"

  # KRaft specific configurations
  KAFKA_CFG_LOG_DIRS: "/bitnami/kafka/data"
  KAFKA_CFG_METADATA_LOG_DIR: "/bitnami/kafka/metadata"
