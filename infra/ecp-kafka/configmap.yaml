apiVersion: v1
kind: ConfigMap
metadata:
  name: ecp-kafka-config
  namespace: default
data:
  # Kafka KRaft mode configuration (without Zookeeper)
  KAFKA_ENABLE_KRAFT: "yes"
  KAFKA_CFG_PROCESS_ROLES: "controller,broker"
  <PERSON><PERSON><PERSON>_CFG_NODE_ID: "1"
  <PERSON><PERSON><PERSON>_CFG_CONTROLLER_QUORUM_VOTERS: "1@ecp-kafka:9093"
  KAFKA_CFG_CONTROLLER_LISTENER_NAMES: "CONTROLLER"
  KAFKA_CFG_LISTENERS: "PLAINTEXT://0.0.0.0:9092,CONTROLLER://0.0.0.0:9093"
  KAF<PERSON>_CFG_ADVERTISED_LISTENERS: "PLAINTEXT://ecp-kafka:9092"
  KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: "CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT"
  KAFKA_CFG_INTER_BROKER_LISTENER_NAME: "PLAINTEXT"
  
  # Basic Kafka configuration
  KAFKA_BROKER_ID: "1"
  ALLOW_PLAINTEXT_LISTENER: "yes"
  TZ: "Asia/Shanghai"
  
  # Kafka JVM optimization - 1.5GB
  KAFKA_HEAP_OPTS: "-Xms512m -Xmx1536m"
  KAFKA_JVM_PERFORMANCE_OPTS: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions"
  
  # Kafka performance optimization
  KAFKA_CFG_NUM_NETWORK_THREADS: "8"
  KAFKA_CFG_NUM_IO_THREADS: "8"
  KAFKA_CFG_SOCKET_SEND_BUFFER_BYTES: "102400"
  KAFKA_CFG_SOCKET_RECEIVE_BUFFER_BYTES: "102400"
  KAFKA_CFG_SOCKET_REQUEST_MAX_BYTES: "104857600"
  
  # KRaft specific configurations
  KAFKA_CFG_LOG_DIRS: "/bitnami/kafka/data"
  KAFKA_CFG_METADATA_LOG_DIR: "/bitnami/kafka/metadata"
