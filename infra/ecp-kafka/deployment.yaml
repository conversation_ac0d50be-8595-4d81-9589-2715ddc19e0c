apiVersion: apps/v1
kind: Deployment
metadata:
  name: ecp-kafka
  namespace: default
  labels:
    app: ecp-kafka
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ecp-kafka
  template:
    metadata:
      labels:
        app: ecp-kafka
    spec:
      containers:
      - name: kafka
        image: bitnami/kafka:3.9
        ports:
        - containerPort: 9092
          name: kafka
        - containerPort: 9093
          name: controller
        envFrom:
        - configMapRef:
            name: ecp-kafka-config
        volumeMounts:
        - name: kafka-data
          mountPath: /bitnami/kafka/data
        - name: kafka-metadata
          mountPath: /bitnami/kafka/metadata
        resources:
          limits:
            memory: "1024Mi"
            cpu: "1500m"
          requests:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - /bin/bash
            - -c
            - "kafka-topics.sh --bootstrap-server localhost:9092 --list"
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - /bin/bash
            - -c
            - "kafka-topics.sh --bootstrap-server localhost:9092 --list"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: kafka-data
        persistentVolumeClaim:
          claimName: ecp-kafka-data
      - name: kafka-metadata
        persistentVolumeClaim:
          claimName: ecp-kafka-metadata
      restartPolicy: Always
