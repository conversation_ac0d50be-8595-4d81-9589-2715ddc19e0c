#!/bin/sh

# 获取管理员密码
ADMIN_PASSWORD=$(cat /nexus-data/admin.password)

# 获取 CSRF Token
CSRF_TOKEN=$(curl -s -u admin:$ADMIN_PASSWORD http://localhost:8081/service/rest/v1/status | jq -r '.csrfToken // empty')

# 设置请求头
HEADERS="-H 'accept: application/json' -H 'Content-Type: application/json'"
if [ ! -z "$CSRF_TOKEN" ]; then
  HEADERS="$HEADERS -H 'NX-ANTI-CSRF-TOKEN: $CSRF_TOKEN'"
fi
HEADERS="$HEADERS -H 'X-Nexus-UI: true'"

# 创建 ecp-snapshots 仓库
curl -X POST -u admin:$ADMIN_PASSWORD "http://localhost:8081/service/rest/v1/repositories/maven/hosted" \
$HEADERS \
  -d '{
"name": "ecp-snapshots",
"online": true,
"storage": {
  "blobStoreName": "default",
  "strictContentTypeValidation": true,
  "writePolicy": "allow"
},
"cleanup": {
  "policyNames": [
    "string"
  ]
},
"component": {
  "proprietaryComponents": true
},
"maven": {
  "versionPolicy": "MIXED",
  "layoutPolicy": "STRICT",
  "contentDisposition": "ATTACHMENT"
}
}'

# 创建 ecp-releases 仓库
curl -X POST -u admin:$ADMIN_PASSWORD "http://localhost:8081/service/rest/v1/repositories/maven/hosted" \
$HEADERS \
  -d '{
"name": "ecp-releases",
"online": true,
"storage": {
  "blobStoreName": "default",
  "strictContentTypeValidation": true,
  "writePolicy": "allow_once"
},
"cleanup": {
  "policyNames": [
    "string"
  ]
},
"component": {
  "proprietaryComponents": true
},
"maven": {
  "versionPolicy": "MIXED",
  "layoutPolicy": "STRICT",
  "contentDisposition": "ATTACHMENT"
}
}'

# 创建 ecp-public 组仓库
curl -X POST -u admin:$ADMIN_PASSWORD "http://localhost:8081/service/rest/v1/repositories/maven/group" \
$HEADERS \
  -d '{
"name": "ecp-public",
"online": true,
"storage": {
  "blobStoreName": "default",
  "strictContentTypeValidation": true
},
"group": {
  "memberNames": [
    "ecp-releases",
    "ecp-snapshots"
  ]
},
"maven": {
  "versionPolicy": "MIXED",
  "layoutPolicy": "STRICT",
  "contentDisposition": "ATTACHMENT"
}
}'