apiVersion: apps/v1
kind: Deployment
metadata:
  name: nexus
  namespace: ecp
  labels:
    app: nexus
    component: repository
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: nexus
  template:
    metadata:
      labels:
        app: nexus
        component: repository
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 200
        runAsGroup: 200
        fsGroup: 200
      containers:
        - name: nexus
#          image: sonatype/nexus3:3.73.0
          image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/sonatypecommunity/nexus3:3.73.0-linuxarm64
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: false
            capabilities:
              drop:
                - ALL
          env:
            - name: TZ
              value: "Asia/Shanghai"
            - name: INSTALL4J_ADD_VM_PARAMS
              value: "-Xms512m -Xmx1536m -XX:MaxDirectMemorySize=512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -XX:+OptimizeStringConcat -Djava.util.prefs.userRoot=/nexus-data/user-prefs -Dnexus.scripts.allowCreation=true -Dnexus.security.randompassword=false"
            - name: NEXUS_SECURITY_RANDOMPASSWORD
              value: "false"
          ports:
            - containerPort: 8081
              name: http
              protocol: TCP
            - containerPort: 8082
              name: docker-http
              protocol: TCP
            - containerPort: 8083
              name: docker-https
              protocol: TCP
          volumeMounts:
            - name: nexus-data
              mountPath: /nexus-data
            - name: nexus-logs
              mountPath: /nexus-data/log
            - name: admin-password
              mountPath: /nexus-data/admin.password
              subPath: admin.password
              readOnly: true
            - name: nexus-config
              mountPath: /nexus-data/etc/nexus.properties
              subPath: nexus.properties
              readOnly: true
            - name: nexus-tmp
              mountPath: /tmp
          resources:
            requests:
              memory: "1024Mi"
              cpu: "500m"
            limits:
              memory: "2560Mi"
              cpu: "1000m"
          livenessProbe:
            httpGet:
              path: /service/rest/v1/status
              port: 8081
              httpHeaders:
                - name: Accept
                  value: application/json
            initialDelaySeconds: 180
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 5
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: /service/rest/v1/status
              port: 8081
              httpHeaders:
                - name: Accept
                  value: application/json
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
            successThreshold: 1
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - "echo 'Gracefully shutting down Nexus...' && sleep 10"
      volumes:
        - name: nexus-data
          persistentVolumeClaim:
            claimName: nexus-pvc
        - name: nexus-logs
          persistentVolumeClaim:
            claimName: nexus-logs-pvc
        - name: admin-password
          secret:
            secretName: nexus-admin-password
        - name: nexus-config
          configMap:
            name: nexus-config
        - name: nexus-tmp
          emptyDir: {}
      restartPolicy: Always