apiVersion: v1
kind: Service
metadata:
  name: nexus-service
  namespace: ecp
  labels:
    app: nexus
spec:
  type: ClusterIP
  ports:
    - port: 8081
      targetPort: 8081
      protocol: TCP
      name: http
    - port: 8082
      targetPort: 8082
      protocol: TCP
      name: docker-http
    - port: 8083
      targetPort: 8083
      protocol: TCP
      name: docker-https
  selector:
    app: nexus
---
apiVersion: v1
kind: Service
metadata:
  name: nexus-console
  namespace: ecp
  labels:
    app: nexus
spec:
  type: NodePort
  ports:
    - port: 8081
      targetPort: 8081
      protocol: TCP
      name: http
      nodePort: 30881
    - port: 8082
      targetPort: 8082
      protocol: TCP
      name: docker-http
      nodePort: 30882
    - port: 8083
      targetPort: 8083
      protocol: TCP
      name: docker-https
      nodePort: 30883
  selector:
    app: nexus