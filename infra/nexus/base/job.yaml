apiVersion: batch/v1
kind: Job
metadata:
  name: nexus-init
  namespace: ecp
  labels:
    app: nexus
spec:
  template:
    metadata:
      labels:
        app: nexus-init
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
      containers:
        - name: nexus-init
          image: curlimages/curl:8.4.0-alpine
          command:
            - /bin/sh
            - -c
            - |
              echo "等待 Nexus 启动..."
              sleep 120
              until curl -f http://nexus-service:8081/service/rest/v1/status; do
                echo "等待 Nexus 就绪..."
                sleep 10
              done
              echo "Nexus 初始化完成"
              if [ -f /scripts/init-repos.sh ]; then
                chmod +x /scripts/init-repos.sh
                /scripts/init-repos.sh
              fi
          env:
            - name: NEXUS_URL
              value: "http://nexus-service:8081"
            - name: NEXUS_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: nexus-admin-password
                  key: admin.password
          volumeMounts:
            - name: init-scripts
              mountPath: /scripts
              readOnly: true
          resources:
            requests:
              memory: "64Mi"
              cpu: "50m"
            limits:
              memory: "128Mi"
              cpu: "100m"
      volumes:
        - name: init-scripts
          configMap:
            name: nexus-init-scripts
            defaultMode: 0755
      restartPolicy: Never
  backoffLimit: 3
  ttlSecondsAfterFinished: 300