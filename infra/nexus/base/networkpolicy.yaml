apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: nexus-network-policy
  namespace: ecp
spec:
  podSelector:
    matchLabels:
      app: nexus
  policyTypes:
    - Ingress
    - Egress
  ingress:
    - from: []  # 允许所有来源
      ports:
        - protocol: TCP
          port: 8081
        - protocol: TCP
          port: 8082
        - protocol: TCP
          port: 8083
  egress:
    - {}  # 允许所有出站流量