apiVersion: v1
kind: PersistentVolume
metadata:
  name: nexus-data-pv
spec:
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 20Gi
  nfs:
    server: 192.168.64.2
    path: /mnt/nfs-share/ecp/ecp-env-k8s/ecp-nexus/data
  persistentVolumeReclaimPolicy: Retain
  storageClassName: nfs-storage
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: nexus-logs-pv
spec:
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 5Gi
  nfs:
    server: 192.168.64.2
    path: /mnt/nfs-share/ecp/ecp-env-k8s/ecp-nexus/log
  persistentVolumeReclaimPolicy: Retain
  storageClassName: nfs-storage
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nexus-pvc
  namespace: ecp
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  volumeName: nexus-data-pv
  storageClassName: nfs-storage
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nexus-logs-pvc
  namespace: ecp
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  volumeName: nexus-logs-pv
  storageClassName: nfs-storage
---
apiVersion: v1
kind: Secret
metadata:
  name: nexus-admin-password
  namespace: ecp
type: Opaque
data:
  admin.password: YWRtaW4xMjM=
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nexus-config
  namespace: ecp
data:
  nexus.properties: |
    application-port=8081
    application-host=0.0.0.0
    nexus-args=${jetty.etc}/jetty.xml,${jetty.etc}/jetty-http.xml,${jetty.etc}/jetty-requestlog.xml
    nexus-context-path=/
    nexus.hazelcast.discovery.isEnabled=true
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nexus
  namespace: ecp
  labels:
    app: nexus
    component: repository
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: nexus
  template:
    metadata:
      labels:
        app: nexus
        component: repository
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8081"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 200
        runAsGroup: 200
        fsGroup: 200
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - nexus
                topologyKey: kubernetes.io/hostname
      containers:
        - name: nexus
          image: sonatype/nexus3:3.73.0
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: false
            capabilities:
              drop:
                - ALL
          env:
            - name: TZ
              value: "Asia/Shanghai"
            - name: INSTALL4J_ADD_VM_PARAMS
              value: "-Xms512m -Xmx1536m -XX:MaxDirectMemorySize=512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -XX:+OptimizeStringConcat -Djava.util.prefs.userRoot=/nexus-data/user-prefs -Dnexus.scripts.allowCreation=true -Dnexus.security.randompassword=false"
            - name: NEXUS_SECURITY_RANDOMPASSWORD
              value: "false"
          ports:
            - containerPort: 8081
              name: http
              protocol: TCP
            - containerPort: 8082
              name: docker-http
              protocol: TCP
            - containerPort: 8083
              name: docker-https
              protocol: TCP
          volumeMounts:
            - name: nexus-data
              mountPath: /nexus-data
            - name: nexus-logs
              mountPath: /nexus-data/log
            - name: admin-password
              mountPath: /nexus-data/admin.password
              subPath: admin.password
              readOnly: true
            - name: nexus-config
              mountPath: /nexus-data/etc/nexus.properties
              subPath: nexus.properties
              readOnly: true
            - name: nexus-tmp
              mountPath: /tmp
          resources:
            requests:
              memory: "1024Mi"
              cpu: "500m"
            limits:
              memory: "2560Mi"
              cpu: "1000m"
          livenessProbe:
            httpGet:
              path: /service/rest/v1/status
              port: 8081
              httpHeaders:
                - name: Accept
                  value: application/json
            initialDelaySeconds: 180
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 5
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: /service/rest/v1/status
              port: 8081
              httpHeaders:
                - name: Accept
                  value: application/json
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
            successThreshold: 1
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - "echo 'Gracefully shutting down Nexus...' && sleep 10"
      volumes:
        - name: nexus-data
          persistentVolumeClaim:
            claimName: nexus-pvc
        - name: nexus-logs
          persistentVolumeClaim:
            claimName: nexus-logs-pvc
        - name: admin-password
          secret:
            secretName: nexus-admin-password
        - name: nexus-config
          configMap:
            name: nexus-config
        - name: nexus-tmp
          emptyDir: {}
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: nexus-service
  namespace: ecp
  labels:
    app: nexus
spec:
  type: ClusterIP
  ports:
    - port: 8081
      targetPort: 8081
      protocol: TCP
      name: http
    - port: 8082
      targetPort: 8082
      protocol: TCP
      name: docker-http
    - port: 8083
      targetPort: 8083
      protocol: TCP
      name: docker-https
  selector:
    app: nexus
---
apiVersion: v1
kind: Service
metadata:
  name: nexus-console
  namespace: ecp
  labels:
    app: nexus
spec:
  type: NodePort
  ports:
    - port: 8081
      targetPort: 8081
      protocol: TCP
      name: http
      nodePort: 30881
    - port: 8082
      targetPort: 8082
      protocol: TCP
      name: docker-http
      nodePort: 30882
    - port: 8083
      targetPort: 8083
      protocol: TCP
      name: docker-https
      nodePort: 30883
  selector:
    app: nexus
---
apiVersion: batch/v1
kind: Job
metadata:
  name: nexus-init
  namespace: ecp
  labels:
    app: nexus
spec:
  template:
    metadata:
      labels:
        app: nexus-init
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
      containers:
        - name: nexus-init
          image: curlimages/curl:8.4.0-alpine
          command:
            - /bin/sh
            - -c
            - |
              echo "等待 Nexus 启动..."
              sleep 120
              until curl -f http://nexus-service:8081/service/rest/v1/status; do
                echo "等待 Nexus 就绪..."
                sleep 10
              done
              echo "Nexus 初始化完成"
              if [ -f /scripts/init-repos.sh ]; then
                chmod +x /scripts/init-repos.sh
                /scripts/init-repos.sh
              fi
          env:
            - name: NEXUS_URL
              value: "http://nexus-service:8081"
            - name: NEXUS_ADMIN_PASSWORD
              value: "admin123"
          volumeMounts:
            - name: init-scripts
              mountPath: /scripts
              readOnly: true
          resources:
            requests:
              memory: "64Mi"
              cpu: "50m"
            limits:
              memory: "128Mi"
              cpu: "100m"
      volumes:
        - name: init-scripts
          configMap:
            name: nexus-init-scripts
      restartPolicy: Never
  backoffLimit: 3
  ttlSecondsAfterFinished: 300
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: nexus-network-policy
  namespace: ecp
spec:
  podSelector:
    matchLabels:
      app: nexus
  policyTypes:
    - Ingress
    - Egress
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: ecp
      ports:
        - protocol: TCP
          port: 8081
        - protocol: TCP
          port: 8082
        - protocol: TCP
          port: 8083
  egress:
    - to:
        - namespaceSelector: {}
      ports:
        - protocol: TCP
          port: 53
        - protocol: UDP
          port: 53